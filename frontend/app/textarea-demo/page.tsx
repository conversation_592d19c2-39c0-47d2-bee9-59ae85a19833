import { TextareaRedHighlightDemo } from '@/components/TextareaRedHighlightDemo';

export default function TextareaDemoPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-2">Textarea with Red Highlighting</h1>
          <p className="text-muted-foreground">
            Demonstrating the textarea component with the exact RGB color from your reference image
          </p>
        </div>
        
        <TextareaRedHighlightDemo />
        
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>
            The red/orange highlighting uses the exact destructive color values from your CSS variables:
          </p>
          <ul className="mt-2 space-y-1">
            <li>Light mode: <code className="bg-muted px-1 rounded">rgb(201, 75, 75)</code></li>
            <li>Dark mode: <code className="bg-muted px-1 rounded">rgb(239, 68, 68)</code></li>
          </ul>
        </div>
      </div>
    </div>
  );
}
