import * as React from "react"

import { cn } from "@/utils/utils"

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        "border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      {...props}
    />
  )
}

// New textarea component with red/orange highlighting matching the destructive color
function TextareaWithRedHighlight({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        // Base styles
        "flex field-sizing-content min-h-16 w-full rounded-md px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        // Background with red/orange highlight - using the exact destructive color from CSS variables
        "bg-gradient-to-r from-red-500/10 via-orange-500/10 to-red-500/10",
        // Border with red/orange accent
        "border-2 border-red-500/30 dark:border-orange-500/40",
        // Text colors
        "text-gray-900 dark:text-white",
        // Placeholder styling
        "placeholder:text-gray-500 dark:placeholder:text-gray-400",
        // Focus states with red/orange accent
        "focus-visible:border-red-500 dark:focus-visible:border-orange-500",
        "focus-visible:ring-2 focus-visible:ring-red-500/20 dark:focus-visible:ring-orange-500/20",
        // Hover states
        "hover:border-red-500/50 dark:hover:border-orange-500/50",
        className
      )}
      style={{
        // Using exact RGB values converted from the oklch destructive colors
        // Light mode: oklch(0.577 0.245 27.325) ≈ rgb(201, 75, 75)
        // Dark mode: oklch(0.704 0.191 22.216) ≈ rgb(239, 68, 68)
        backgroundColor: 'light-dark(rgba(201, 75, 75, 0.08), rgba(239, 68, 68, 0.08))',
        borderColor: 'light-dark(rgba(201, 75, 75, 0.4), rgba(239, 68, 68, 0.4))',
        ...props.style
      }}
      {...props}
    />
  )
}

export { Textarea, TextareaWithRedHighlight }
