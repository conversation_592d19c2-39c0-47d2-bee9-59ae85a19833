import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { X, Wand2, <PERSON>ader2, ArrowUp, PlusCircle, MoreHorizontal } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { cn } from "@/utils/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { enhanceImagePrompt } from "@/services/api";
import { toast } from "sonner";

import { useImageSettings } from "@/context/image-settings-context";
import { useTheme } from "next-themes";


interface ImageOverlayProps {
  onGenerate: (settings: {
    prompt: string;
    imageSize: string;
    saveImages: boolean;
    mode: string;
    brandsProtection: string;
    brandProtectionModel: string;
    variations: number;
    folder: string;
    background: string;
    outputFormat: string;
    quality: string;
    sourceImages?: File[];
    brandsList?: string[];
  }) => void;
  isGenerating?: boolean;
  onPromptChange?: (newPrompt: string, isEnhanced: boolean) => void;
}

export function ImageOverlay({ 
  onGenerate, 
  isGenerating = false, 
  onPromptChange,

}: ImageOverlayProps) {
  const [prompt, setPrompt] = useState("");
  const [imageSize, setImageSize] = useState("1024x1024");
  const [saveImages] = useState(true);
  const [mode] = useState("prod");
  const imageSettings = useImageSettings();
  const [variations, setVariations] = useState([1]);
  const [isWizardEnhancing, setIsWizardEnhancing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const [background, setBackground] = useState("auto");
  const [outputFormat, setOutputFormat] = useState("png");
  const [quality, setQuality] = useState("auto");
  const [sourceImages, setSourceImages] = useState<File[]>([]);
  
  // Reference to the textarea element
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Add theme context
  const { theme, resolvedTheme } = useTheme();
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  
  // Move theme detection to useEffect to prevent hydration mismatch
  useEffect(() => {
    // Only run on client-side
    setIsDarkTheme(
      resolvedTheme === 'dark' || 
      theme === 'dark' || 
      (!theme && !resolvedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)
    );
  }, [theme, resolvedTheme]);


  
  // Resize textarea when prompt changes (especially after AI enhancement)
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      // Only auto-resize if we haven't hit the max height
      const scrollHeight = textareaRef.current.scrollHeight;
      if (scrollHeight <= 200) {
        textareaRef.current.style.height = `${scrollHeight}px`;
      } else {
        textareaRef.current.style.height = '200px';
      }
    }
  }, [prompt]);

  // Effect to handle format compatibility with transparent background
  useEffect(() => {
    if (background === "transparent" && outputFormat === "jpeg") {
      setOutputFormat("png");
    }
  }, [background, outputFormat]);

  // Get overlay background color based on theme
  const getOverlayBgColor = () => {
    return isDarkTheme 
      ? 'backdrop-blur-md bg-black/70 border-white/10' 
      : 'backdrop-blur-md bg-white/90 border-black/10 shadow-lg';
  };
  
  // Get input and control background color based on theme
  const getControlBgColor = () => {
    return isDarkTheme
      ? 'bg-black/30 border-0 text-white focus:ring-white/20'
      : 'bg-white/50 border-gray-200 text-gray-900 focus:ring-gray-200';
  };
  
  // Get text color based on theme
  const getTextColor = () => {
    return isDarkTheme ? 'text-white' : 'text-gray-900';
  };
  
  // Get muted text color based on theme
  const getMutedTextColor = () => {
    return isDarkTheme ? 'text-white/70' : 'text-gray-500';
  };

  // Get hover background color based on theme
  const getHoverBgColor = () => {
    return isDarkTheme ? 'hover:bg-white/10' : 'hover:bg-gray-200/50';
  };

  const handleSubmit = () => {
    if (prompt.trim() === "") {
      toast.error("Please enter a prompt");
      return;
    }

    const numVariations = variations[0];

    if (numVariations < 1 || numVariations > 10) {
      toast.error("Please select a valid number of variations (1-10)");
      return;
    }

    onGenerate({
      prompt,
      imageSize,
      saveImages,
      mode,
      brandsProtection: imageSettings.settings.brandsProtection,
      brandProtectionModel: "GPT-4o",
      variations: numVariations,
      folder: "root",
      background,
      outputFormat,
      quality,
      sourceImages,
      brandsList: imageSettings.settings.brandsList
    });
  };

  const handleWizardEnhance = async () => {
    if (!prompt.trim() || isGenerating || isWizardEnhancing) return;
    
    // Set loading state
    setIsWizardEnhancing(true);
    
    try {
      // Call the API to enhance the prompt
      const enhancedPrompt = await enhanceImagePrompt(prompt.trim());
      
      // Update the prompt with the enhanced version
      setPrompt(enhancedPrompt);
      
      // Notify parent component about the prompt change
      if (onPromptChange) {
        onPromptChange(enhancedPrompt, true);
      }
      
      // Show success message
      toast.success("Prompt enhanced", {
        description: "Your prompt has been enhanced with AI"
      });
    } catch (error) {
      console.error("Error enhancing prompt:", error);
      toast.error("Failed to enhance prompt", {
        description: "Please try again or adjust your prompt"
      });
    } finally {
      // Reset loading state
      setIsWizardEnhancing(false);
    }
  };



  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const validFiles: File[] = [];
      
      for (const file of files) {
        // Validate file type
        if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
          toast.error("Invalid file type", {
            description: `${file.name}: Only JPEG, PNG, and WebP images are supported`
          });
          continue;
        }
        
        // Validate file size
        if (file.size > 25 * 1024 * 1024) {
          toast.error("File too large", {
            description: `${file.name}: Images must be less than 25MB`
          });
          continue;
        }
        
        validFiles.push(file);
      }
      
      // Limit to 5 images total (gpt-image-1 supports up to 10, but we'll be conservative)
      if (sourceImages.length + validFiles.length > 5) {
        toast.warning("Too many images", {
          description: "Maximum 5 images can be selected"
        });
        
        // Take only what we can fit
        const spaceLeft = 5 - sourceImages.length;
        validFiles.splice(spaceLeft);
      }
      
      if (validFiles.length > 0) {
        setSourceImages(prev => [...prev, ...validFiles]);
        
        toast.success("Images selected", {
          description: `Added ${validFiles.length} image${validFiles.length > 1 ? 's' : ''}`
        });
      }
    }
  };
  
  // Handle image removal - now removes a specific image by index
  const handleRemoveImage = (index: number) => {
    setSourceImages(prev => prev.filter((_, i) => i !== index));
  };
  
  // Handle clearing all images
  const handleClearAllImages = () => {
    setSourceImages([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="sticky bottom-0 left-0 right-0 flex items-end justify-center p-6 z-20 pointer-events-none">
      <div className="w-full max-w-4xl transition-all duration-200 ease-in-out pointer-events-auto mb-6 relative">
        {/* Image thumbnails row - positioned above the main container */}
        {sourceImages.length > 0 && (
          <div className="flex flex-wrap gap-2 items-center mb-4">
            {sourceImages.map((img, index) => (
              <div key={index} className="relative">
                <img
                  src={URL.createObjectURL(img)}
                  alt={`Image ${index + 1}`}
                  className="w-12 h-12 object-cover rounded-md border border-gray-500/30"
                />
                <Button
                  onClick={() => handleRemoveImage(index)}
                  className={cn(
                    "absolute -top-2 -right-2 rounded-full p-0.5 hover:bg-black",
                    isDarkTheme ? "bg-black/70 text-white" : "bg-white/90 text-gray-700"
                  )}
                  disabled={isGenerating}
                  aria-label="Remove image"
                  title="Remove image"
                  variant="ghost"
                  size="icon"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
            {sourceImages.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAllImages}
                className={cn(
                  "text-xs",
                  getMutedTextColor(),
                  getHoverBgColor()
                )}
                disabled={isGenerating}
              >
                Clear all
              </Button>
            )}
          </div>
        )}

        {/* Main prompt input container - compact design matching reference */}
        <div className={cn(
          "rounded-2xl px-4 py-3 shadow-lg relative",
          "border border-gray-700/50",
          isDarkTheme
            ? "bg-gray-900/95"
            : "bg-gray-100/95"
        )}>
          <Textarea
            value={prompt}
            onChange={(e) => {
              setPrompt(e.target.value);
              if (onPromptChange) {
                onPromptChange(e.target.value, false);
              }
            }}
            placeholder="Enter a text prompt or reference image"
            className={cn(
              "border-0 min-h-[50px] max-h-[120px] resize-none px-0 py-0 pr-40 overflow-y-auto bg-transparent focus:ring-0 focus:outline-none shadow-none",
              isDarkTheme ? "text-white placeholder:text-gray-400" : "text-gray-900 placeholder:text-gray-500"
            )}
            disabled={isGenerating}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && prompt.trim() && !isGenerating) {
                e.preventDefault();
                handleSubmit();
              }
            }}
            rows={2}
            ref={textareaRef}
            style={{
              boxShadow: 'none',
              outline: 'none',
              border: 'none',
              background: 'transparent'
            }}
          />

          {/* Upload button inside textarea */}
          <TooltipProvider>
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    if (fileInputRef.current) {
                      fileInputRef.current.click();
                    }
                  }}
                  aria-label="Upload images"
                  className={cn(
                    "absolute bottom-3 left-3",
                    getMutedTextColor(),
                    getHoverBgColor()
                  )}
                  disabled={isGenerating}
                >
                  <PlusCircle className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="font-medium">
                <p>Upload images to edit (max 5)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Action buttons in bottom right of textarea */}
          <div className="absolute bottom-3 right-3 flex items-center gap-2">
            {/* Settings toggle button */}
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowSettings(!showSettings)}
                    aria-label="Toggle settings"
                    className={cn(
                      "h-8 w-8",
                      getMutedTextColor(),
                      getHoverBgColor()
                    )}
                    disabled={isGenerating}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="font-medium">
                  <p>More options</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Enhance button */}
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleWizardEnhance}
                    aria-label="Enhance prompt"
                    className={cn(
                      "h-8 w-8",
                      getMutedTextColor(),
                      getHoverBgColor()
                    )}
                    disabled={isGenerating || isWizardEnhancing || !prompt.trim()}
                  >
                    {isWizardEnhancing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Wand2 className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="font-medium">
                  <p>Enhance your prompt with AI</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Generate button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleSubmit}
              className={cn(
                "border-0 h-8",
                isDarkTheme
                  ? "bg-white/10 hover:bg-white/20 text-white"
                  : "bg-gray-100 hover:bg-gray-200 text-gray-900"
              )}
              disabled={isGenerating || !prompt.trim()}
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <ArrowUp className="h-4 w-4 mr-2" />
              )}
              {isGenerating ? "Processing..." : sourceImages.length > 0 ? "Edit Images" : "Generate"}
            </Button>
          </div>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileSelect}
            accept="image/jpeg,image/png,image/webp"
            className="hidden"
            disabled={isGenerating}
            aria-label="Upload image files"
            multiple
          />
        </div>

        {/* Settings panel - appears above the main container when showSettings is true */}
        {showSettings && (
          <div className={cn(
            "absolute bottom-full right-0 mb-4 w-80 p-4 rounded-xl shadow-lg border z-10 max-h-96 overflow-y-auto",
            getOverlayBgColor()
          )}>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <h3 className={cn("text-sm font-medium", getTextColor())}>Settings</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowSettings(false)}
                  className={cn("h-6 w-6", getMutedTextColor(), getHoverBgColor())}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <TooltipProvider>
                {/* Aspect Ratio Dropdown */}
                <div className="space-y-2">
                  <label className={cn("text-sm", getMutedTextColor())}>Aspect Ratio</label>
                  <Select
                    value={imageSize}
                    onValueChange={setImageSize}
                    disabled={isGenerating}
                  >
                    <SelectTrigger className={cn("w-full h-8", getControlBgColor())}>
                      <SelectValue placeholder="16:9" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1024x1024">1:1 (1024x1024)</SelectItem>
                      <SelectItem value="1536x1024">3:2 (1536x1024)</SelectItem>
                      <SelectItem value="1024x1536">2:3 (1024x1536)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Batch Size Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className={cn("text-sm", getMutedTextColor())}>Batch Size</label>
                    <span className={cn("text-sm font-medium", getTextColor())}>{variations[0]}</span>
                  </div>
                  <Slider
                    value={variations}
                    onValueChange={setVariations}
                    max={10}
                    min={1}
                    step={1}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>

                {/* Safety Tolerance Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className={cn("text-sm", getMutedTextColor())}>Safety Tolerance</label>
                    <span className={cn("text-sm font-medium", getTextColor())}>2</span>
                  </div>
                  <Slider
                    value={[2]}
                    max={5}
                    min={1}
                    step={1}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>

                {/* Prompt Upsampling Toggle */}
                <div className="flex items-center justify-between">
                  <label className={cn("text-sm", getMutedTextColor())}>Prompt Upsampling</label>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 w-10 rounded-full p-0",
                      isDarkTheme ? "bg-white/20" : "bg-gray-200"
                    )}
                  >
                    <div className={cn(
                      "h-4 w-4 rounded-full transition-transform",
                      isDarkTheme ? "bg-white translate-x-2" : "bg-gray-600 -translate-x-2"
                    )} />
                  </Button>
                </div>

                {/* Output Format Dropdown */}
                <div className="space-y-2">
                  <label className={cn("text-sm", getMutedTextColor())}>Output Format</label>
                  <Select
                    value={outputFormat}
                    onValueChange={setOutputFormat}
                    disabled={isGenerating}
                  >
                    <SelectTrigger className={cn("w-full h-8", getControlBgColor())}>
                      <SelectValue placeholder="PNG" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="jpeg">JPEG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Seed */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className={cn("text-sm", getMutedTextColor())}>Seed</label>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn("text-xs", getMutedTextColor())}
                    >
                      Random
                    </Button>
                  </div>
                </div>
              </TooltipProvider>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}