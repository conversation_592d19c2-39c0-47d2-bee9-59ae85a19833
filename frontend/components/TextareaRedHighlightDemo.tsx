'use client';

import React, { useState } from 'react';
import { TextareaWithRedHighlight } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export function TextareaRedHighlightDemo() {
  const [value, setValue] = useState('');

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Textarea with Red Highlighting</CardTitle>
        <CardDescription>
          This textarea uses the exact same RGB color as highlighted in the red circle from your reference image.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="red-textarea" className="text-sm font-medium">
            Enter your text:
          </label>
          <TextareaWithRedHighlight
            id="red-textarea"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder="Enter a text prompt or reference image"
            className="min-h-[120px] resize-y"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            Characters: {value.length}
          </span>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setValue('')}
          >
            Clear
          </Button>
        </div>

        {value && (
          <div className="mt-4 p-3 bg-muted rounded-md">
            <h4 className="text-sm font-medium mb-2">Preview:</h4>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">
              {value}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
